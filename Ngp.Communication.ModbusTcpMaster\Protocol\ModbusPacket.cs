using System.Buffers.Binary;

namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Represents a Modbus TCP packet with proper header and data handling
/// Ensures compliance with Modbus TCP specification
/// </summary>
public class ModbusPacket
{
    // Modbus TCP Header constants
    private const int TcpHeaderLength = 6;
    private const int MinPacketLength = TcpHeaderLength + 2; // Header + SlaveId + FunctionCode
    private const int MaxPacketLength = 260; // As per Modbus specification
    private const ushort ProtocolIdentifier = 0x0000; // Always 0 for Modbus TCP

    /// <summary>
    /// Transaction identifier for matching requests and responses
    /// </summary>
    public ushort TransactionId { get; set; }

    /// <summary>
    /// Protocol identifier (always 0 for Modbus TCP)
    /// </summary>
    public ushort ProtocolId { get; set; } = ProtocolIdentifier;

    /// <summary>
    /// Length of the following bytes (Unit ID + PDU)
    /// </summary>
    public ushort Length { get; set; }

    /// <summary>
    /// Unit identifier (slave address)
    /// </summary>
    public byte UnitId { get; set; }

    /// <summary>
    /// Function code
    /// </summary>
    public byte FunctionCode { get; set; }

    /// <summary>
    /// Protocol Data Unit (PDU) - data portion of the packet
    /// </summary>
    public byte[] Data { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// Create a new Modbus TCP packet
    /// </summary>
    public ModbusPacket()
    {
    }

    /// <summary>
    /// Create a new Modbus TCP packet with specified parameters
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier (slave address)</param>
    /// <param name="functionCode">Function code</param>
    /// <param name="data">Data bytes</param>
    public ModbusPacket(ushort transactionId, byte unitId, byte functionCode, byte[]? data = null)
    {
        TransactionId = transactionId;
        UnitId = unitId;
        FunctionCode = functionCode;
        Data = data ?? Array.Empty<byte>();
        Length = (ushort)(1 + 1 + Data.Length); // UnitId + FunctionCode + Data
    }

    /// <summary>
    /// Serialize the packet to byte array for transmission
    /// Ensures proper byte order and format according to Modbus TCP specification
    /// </summary>
    /// <returns>Serialized packet bytes</returns>
    public byte[] ToByteArray()
    {
        var totalLength = TcpHeaderLength + 1 + 1 + Data.Length; // Header + UnitId + FunctionCode + Data
        var buffer = new byte[totalLength];
        var span = buffer.AsSpan();

        // Modbus TCP Header (6 bytes)
        BinaryPrimitives.WriteUInt16BigEndian(span[0..2], TransactionId);
        BinaryPrimitives.WriteUInt16BigEndian(span[2..4], ProtocolId);
        BinaryPrimitives.WriteUInt16BigEndian(span[4..6], Length);

        // Unit ID
        buffer[6] = UnitId;

        // Function Code
        buffer[7] = FunctionCode;

        // Data
        if (Data.Length > 0)
        {
            Data.CopyTo(buffer, 8);
        }

        return buffer;
    }

    /// <summary>
    /// Parse a byte array into a Modbus TCP packet
    /// Validates packet format and ensures compliance with Modbus TCP specification
    /// </summary>
    /// <param name="buffer">Buffer containing packet data</param>
    /// <returns>Parsed Modbus packet</returns>
    /// <exception cref="ArgumentException">Thrown when packet format is invalid</exception>
    public static ModbusPacket Parse(byte[] buffer)
    {
        return Parse(buffer.AsSpan());
    }

    /// <summary>
    /// Parse a span of bytes into a Modbus TCP packet
    /// Validates packet format and ensures compliance with Modbus TCP specification
    /// </summary>
    /// <param name="buffer">Span containing packet data</param>
    /// <returns>Parsed Modbus packet</returns>
    /// <exception cref="ArgumentException">Thrown when packet format is invalid</exception>
    public static ModbusPacket Parse(ReadOnlySpan<byte> buffer)
    {
        // Validate minimum packet length
        if (buffer.Length < MinPacketLength)
        {
            throw new ArgumentException($"Packet too short. Minimum length is {MinPacketLength} bytes, received {buffer.Length} bytes.");
        }

        // Validate maximum packet length
        if (buffer.Length > MaxPacketLength)
        {
            throw new ArgumentException($"Packet too long. Maximum length is {MaxPacketLength} bytes, received {buffer.Length} bytes.");
        }

        // Parse TCP header
        var transactionId = BinaryPrimitives.ReadUInt16BigEndian(buffer[0..2]);
        var protocolId = BinaryPrimitives.ReadUInt16BigEndian(buffer[2..4]);
        var length = BinaryPrimitives.ReadUInt16BigEndian(buffer[4..6]);

        // Validate protocol identifier
        if (protocolId != ProtocolIdentifier)
        {
            throw new ArgumentException($"Invalid protocol identifier. Expected {ProtocolIdentifier}, received {protocolId}.");
        }

        // Validate length field
        var expectedLength = buffer.Length - TcpHeaderLength;
        if (length != expectedLength)
        {
            throw new ArgumentException($"Length field mismatch. Expected {expectedLength}, received {length}.");
        }

        // Parse Unit ID and Function Code
        var unitId = buffer[6];
        var functionCode = buffer[7];

        // Validate function code
        if (!ModbusFunctionCodes.IsValidFunctionCode(functionCode))
        {
            throw new ArgumentException($"Invalid function code: 0x{functionCode:X2}");
        }

        // Extract data
        var dataLength = buffer.Length - 8; // Total length - header - unitId - functionCode
        var data = dataLength > 0 ? buffer[8..].ToArray() : Array.Empty<byte>();

        return new ModbusPacket
        {
            TransactionId = transactionId,
            ProtocolId = protocolId,
            Length = length,
            UnitId = unitId,
            FunctionCode = functionCode,
            Data = data
        };
    }

    /// <summary>
    /// Check if this packet is an exception response
    /// </summary>
    /// <returns>True if this is an exception response</returns>
    public bool IsExceptionResponse()
    {
        return ModbusFunctionCodes.IsExceptionResponse(FunctionCode);
    }

    /// <summary>
    /// Get the exception code if this is an exception response
    /// </summary>
    /// <returns>Exception code, or null if not an exception response</returns>
    public byte? GetExceptionCode()
    {
        if (IsExceptionResponse() && Data.Length > 0)
        {
            return Data[0];
        }
        return null;
    }

    /// <summary>
    /// Create an exception response packet
    /// </summary>
    /// <param name="originalPacket">Original request packet</param>
    /// <param name="exceptionCode">Exception code to return</param>
    /// <returns>Exception response packet</returns>
    public static ModbusPacket CreateExceptionResponse(ModbusPacket originalPacket, byte exceptionCode)
    {
        var exceptionFunctionCode = ModbusFunctionCodes.CreateExceptionResponse(originalPacket.FunctionCode);
        return new ModbusPacket(
            originalPacket.TransactionId,
            originalPacket.UnitId,
            exceptionFunctionCode,
            new[] { exceptionCode }
        );
    }

    /// <summary>
    /// Validate packet integrity and format
    /// </summary>
    /// <returns>True if packet is valid</returns>
    public bool IsValid()
    {
        try
        {
            // Check protocol identifier
            if (ProtocolId != ProtocolIdentifier)
                return false;

            // Check length field consistency
            if (Length != 1 + 1 + Data.Length)
                return false;

            // Check function code validity
            if (!ModbusFunctionCodes.IsValidFunctionCode(FunctionCode))
                return false;

            // Additional validation for exception responses
            if (IsExceptionResponse())
            {
                if (Data.Length != 1)
                    return false;

                if (!ModbusExceptionCodes.IsValidExceptionCode(Data[0]))
                    return false;
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Get a string representation of the packet for debugging
    /// </summary>
    /// <returns>String representation of the packet</returns>
    public override string ToString()
    {
        var dataHex = Data.Length > 0 ? Convert.ToHexString(Data) : "None";
        var exceptionInfo = IsExceptionResponse() ? $" (Exception: {GetExceptionCode()})" : "";
        
        return $"ModbusPacket [TxId: {TransactionId}, Unit: {UnitId}, Func: 0x{FunctionCode:X2}{exceptionInfo}, Length: {Length}, Data: {dataHex}]";
    }
}
