using System.Buffers.Binary;

namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Parser for Modbus response packets ensuring compliance with Modbus specification
/// </summary>
public static class ModbusResponseParser
{
    /// <summary>
    /// Parse a Read Coils response (Function Code 0x01)
    /// </summary>
    /// <param name="packet">Response packet</param>
    /// <param name="expectedQuantity">Expected number of coils</param>
    /// <returns>Array of coil values</returns>
    /// <exception cref="ArgumentException">Thrown when response format is invalid</exception>
    public static bool[] ParseReadCoilsResponse(ModbusPacket packet, ushort expectedQuantity)
    {
        ValidateResponsePacket(packet, ModbusFunctionCodes.ReadCoils);
        
        if (packet.Data.Length < 1)
            throw new ArgumentException("Read Coils response must contain byte count");
        
        var byteCount = packet.Data[0];
        var expectedByteCount = (expectedQuantity + 7) / 8; // Round up to nearest byte
        
        if (byteCount != expectedByteCount)
            throw new ArgumentException($"Invalid byte count. Expected: {expectedByteCount}, Received: {byteCount}");
        
        if (packet.Data.Length != 1 + byteCount)
            throw new ArgumentException($"Invalid data length. Expected: {1 + byteCount}, Received: {packet.Data.Length}");
        
        var coils = new bool[expectedQuantity];
        
        for (int i = 0; i < expectedQuantity; i++)
        {
            var byteIndex = 1 + (i / 8);
            var bitIndex = i % 8;
            coils[i] = (packet.Data[byteIndex] & (1 << bitIndex)) != 0;
        }
        
        return coils;
    }

    /// <summary>
    /// Parse a Read Discrete Inputs response (Function Code 0x02)
    /// </summary>
    /// <param name="packet">Response packet</param>
    /// <param name="expectedQuantity">Expected number of discrete inputs</param>
    /// <returns>Array of discrete input values</returns>
    /// <exception cref="ArgumentException">Thrown when response format is invalid</exception>
    public static bool[] ParseReadDiscreteInputsResponse(ModbusPacket packet, ushort expectedQuantity)
    {
        ValidateResponsePacket(packet, ModbusFunctionCodes.ReadDiscreteInputs);
        
        if (packet.Data.Length < 1)
            throw new ArgumentException("Read Discrete Inputs response must contain byte count");
        
        var byteCount = packet.Data[0];
        var expectedByteCount = (expectedQuantity + 7) / 8; // Round up to nearest byte
        
        if (byteCount != expectedByteCount)
            throw new ArgumentException($"Invalid byte count. Expected: {expectedByteCount}, Received: {byteCount}");
        
        if (packet.Data.Length != 1 + byteCount)
            throw new ArgumentException($"Invalid data length. Expected: {1 + byteCount}, Received: {packet.Data.Length}");
        
        var inputs = new bool[expectedQuantity];
        
        for (int i = 0; i < expectedQuantity; i++)
        {
            var byteIndex = 1 + (i / 8);
            var bitIndex = i % 8;
            inputs[i] = (packet.Data[byteIndex] & (1 << bitIndex)) != 0;
        }
        
        return inputs;
    }

    /// <summary>
    /// Parse a Read Holding Registers response (Function Code 0x03)
    /// </summary>
    /// <param name="packet">Response packet</param>
    /// <param name="expectedQuantity">Expected number of registers</param>
    /// <returns>Array of register values</returns>
    /// <exception cref="ArgumentException">Thrown when response format is invalid</exception>
    public static ushort[] ParseReadHoldingRegistersResponse(ModbusPacket packet, ushort expectedQuantity)
    {
        ValidateResponsePacket(packet, ModbusFunctionCodes.ReadHoldingRegisters);
        
        if (packet.Data.Length < 1)
            throw new ArgumentException("Read Holding Registers response must contain byte count");
        
        var byteCount = packet.Data[0];
        var expectedByteCount = expectedQuantity * 2;
        
        if (byteCount != expectedByteCount)
            throw new ArgumentException($"Invalid byte count. Expected: {expectedByteCount}, Received: {byteCount}");
        
        if (packet.Data.Length != 1 + byteCount)
            throw new ArgumentException($"Invalid data length. Expected: {1 + byteCount}, Received: {packet.Data.Length}");
        
        var registers = new ushort[expectedQuantity];
        
        for (int i = 0; i < expectedQuantity; i++)
        {
            var offset = 1 + (i * 2);
            registers[i] = BinaryPrimitives.ReadUInt16BigEndian(packet.Data.AsSpan(offset, 2));
        }
        
        return registers;
    }

    /// <summary>
    /// Parse a Read Input Registers response (Function Code 0x04)
    /// </summary>
    /// <param name="packet">Response packet</param>
    /// <param name="expectedQuantity">Expected number of registers</param>
    /// <returns>Array of register values</returns>
    /// <exception cref="ArgumentException">Thrown when response format is invalid</exception>
    public static ushort[] ParseReadInputRegistersResponse(ModbusPacket packet, ushort expectedQuantity)
    {
        ValidateResponsePacket(packet, ModbusFunctionCodes.ReadInputRegisters);
        
        if (packet.Data.Length < 1)
            throw new ArgumentException("Read Input Registers response must contain byte count");
        
        var byteCount = packet.Data[0];
        var expectedByteCount = expectedQuantity * 2;
        
        if (byteCount != expectedByteCount)
            throw new ArgumentException($"Invalid byte count. Expected: {expectedByteCount}, Received: {byteCount}");
        
        if (packet.Data.Length != 1 + byteCount)
            throw new ArgumentException($"Invalid data length. Expected: {1 + byteCount}, Received: {packet.Data.Length}");
        
        var registers = new ushort[expectedQuantity];
        
        for (int i = 0; i < expectedQuantity; i++)
        {
            var offset = 1 + (i * 2);
            registers[i] = BinaryPrimitives.ReadUInt16BigEndian(packet.Data.AsSpan(offset, 2));
        }
        
        return registers;
    }

    /// <summary>
    /// Parse a Write Single Coil response (Function Code 0x05)
    /// </summary>
    /// <param name="packet">Response packet</param>
    /// <param name="expectedAddress">Expected coil address</param>
    /// <param name="expectedValue">Expected coil value</param>
    /// <returns>True if response is valid</returns>
    /// <exception cref="ArgumentException">Thrown when response format is invalid</exception>
    public static bool ParseWriteSingleCoilResponse(ModbusPacket packet, ushort expectedAddress, bool expectedValue)
    {
        ValidateResponsePacket(packet, ModbusFunctionCodes.WriteSingleCoil);
        
        if (packet.Data.Length != 4)
            throw new ArgumentException($"Write Single Coil response must be 4 bytes. Received: {packet.Data.Length}");
        
        var address = BinaryPrimitives.ReadUInt16BigEndian(packet.Data.AsSpan(0, 2));
        var value = BinaryPrimitives.ReadUInt16BigEndian(packet.Data.AsSpan(2, 2));
        
        if (address != expectedAddress)
            throw new ArgumentException($"Address mismatch. Expected: {expectedAddress}, Received: {address}");
        
        var expectedValueCode = expectedValue ? 0xFF00 : 0x0000;
        if (value != expectedValueCode)
            throw new ArgumentException($"Value mismatch. Expected: 0x{expectedValueCode:X4}, Received: 0x{value:X4}");
        
        return true;
    }

    /// <summary>
    /// Parse a Write Single Register response (Function Code 0x06)
    /// </summary>
    /// <param name="packet">Response packet</param>
    /// <param name="expectedAddress">Expected register address</param>
    /// <param name="expectedValue">Expected register value</param>
    /// <returns>True if response is valid</returns>
    /// <exception cref="ArgumentException">Thrown when response format is invalid</exception>
    public static bool ParseWriteSingleRegisterResponse(ModbusPacket packet, ushort expectedAddress, ushort expectedValue)
    {
        ValidateResponsePacket(packet, ModbusFunctionCodes.WriteSingleRegister);
        
        if (packet.Data.Length != 4)
            throw new ArgumentException($"Write Single Register response must be 4 bytes. Received: {packet.Data.Length}");
        
        var address = BinaryPrimitives.ReadUInt16BigEndian(packet.Data.AsSpan(0, 2));
        var value = BinaryPrimitives.ReadUInt16BigEndian(packet.Data.AsSpan(2, 2));
        
        if (address != expectedAddress)
            throw new ArgumentException($"Address mismatch. Expected: {expectedAddress}, Received: {address}");
        
        if (value != expectedValue)
            throw new ArgumentException($"Value mismatch. Expected: {expectedValue}, Received: {value}");
        
        return true;
    }

    /// <summary>
    /// Parse a Write Multiple Coils response (Function Code 0x0F)
    /// </summary>
    /// <param name="packet">Response packet</param>
    /// <param name="expectedStartAddress">Expected starting address</param>
    /// <param name="expectedQuantity">Expected quantity of coils</param>
    /// <returns>True if response is valid</returns>
    /// <exception cref="ArgumentException">Thrown when response format is invalid</exception>
    public static bool ParseWriteMultipleCoilsResponse(ModbusPacket packet, ushort expectedStartAddress, ushort expectedQuantity)
    {
        ValidateResponsePacket(packet, ModbusFunctionCodes.WriteMultipleCoils);
        
        if (packet.Data.Length != 4)
            throw new ArgumentException($"Write Multiple Coils response must be 4 bytes. Received: {packet.Data.Length}");
        
        var startAddress = BinaryPrimitives.ReadUInt16BigEndian(packet.Data.AsSpan(0, 2));
        var quantity = BinaryPrimitives.ReadUInt16BigEndian(packet.Data.AsSpan(2, 2));
        
        if (startAddress != expectedStartAddress)
            throw new ArgumentException($"Start address mismatch. Expected: {expectedStartAddress}, Received: {startAddress}");
        
        if (quantity != expectedQuantity)
            throw new ArgumentException($"Quantity mismatch. Expected: {expectedQuantity}, Received: {quantity}");
        
        return true;
    }

    /// <summary>
    /// Parse a Write Multiple Registers response (Function Code 0x10)
    /// </summary>
    /// <param name="packet">Response packet</param>
    /// <param name="expectedStartAddress">Expected starting address</param>
    /// <param name="expectedQuantity">Expected quantity of registers</param>
    /// <returns>True if response is valid</returns>
    /// <exception cref="ArgumentException">Thrown when response format is invalid</exception>
    public static bool ParseWriteMultipleRegistersResponse(ModbusPacket packet, ushort expectedStartAddress, ushort expectedQuantity)
    {
        ValidateResponsePacket(packet, ModbusFunctionCodes.WriteMultipleRegisters);
        
        if (packet.Data.Length != 4)
            throw new ArgumentException($"Write Multiple Registers response must be 4 bytes. Received: {packet.Data.Length}");
        
        var startAddress = BinaryPrimitives.ReadUInt16BigEndian(packet.Data.AsSpan(0, 2));
        var quantity = BinaryPrimitives.ReadUInt16BigEndian(packet.Data.AsSpan(2, 2));
        
        if (startAddress != expectedStartAddress)
            throw new ArgumentException($"Start address mismatch. Expected: {expectedStartAddress}, Received: {startAddress}");
        
        if (quantity != expectedQuantity)
            throw new ArgumentException($"Quantity mismatch. Expected: {expectedQuantity}, Received: {quantity}");
        
        return true;
    }

    /// <summary>
    /// Validate response packet basic format
    /// </summary>
    /// <param name="packet">Packet to validate</param>
    /// <param name="expectedFunctionCode">Expected function code</param>
    /// <exception cref="ArgumentException">Thrown when packet is invalid</exception>
    /// <exception cref="ModbusException">Thrown when packet contains Modbus exception</exception>
    private static void ValidateResponsePacket(ModbusPacket packet, byte expectedFunctionCode)
    {
        if (packet == null)
            throw new ArgumentException("Packet cannot be null");
        
        if (packet.IsExceptionResponse())
        {
            var originalFunctionCode = ModbusFunctionCodes.GetOriginalFunctionCode(packet.FunctionCode);
            if (originalFunctionCode == expectedFunctionCode)
            {
                var exceptionCode = packet.GetExceptionCode() ?? 0;
                throw new ModbusException(exceptionCode, originalFunctionCode, packet.UnitId);
            }
        }
        
        if (packet.FunctionCode != expectedFunctionCode)
            throw new ArgumentException($"Function code mismatch. Expected: 0x{expectedFunctionCode:X2}, Received: 0x{packet.FunctionCode:X2}");
    }
}
