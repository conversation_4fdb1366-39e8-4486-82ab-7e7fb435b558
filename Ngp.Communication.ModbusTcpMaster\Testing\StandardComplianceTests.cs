using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Protocol;

namespace Ngp.Communication.ModbusTcpMaster.Testing;

/// <summary>
/// Comprehensive tests to ensure Modbus implementation complies with official standards
/// Tests based on Modbus Application Protocol Specification V1.1b3 and Modbus TCP Implementation Guide V1.0b
/// </summary>
public class StandardComplianceTests
{
    private readonly ModbusPacketValidator _validator;
    private readonly ILogger<StandardComplianceTests> _logger;

    public StandardComplianceTests(ILogger<StandardComplianceTests> logger)
    {
        _logger = logger;
        var validatorLogger = logger as ILogger<ModbusPacketValidator> ??
            Microsoft.Extensions.Logging.Abstractions.NullLogger<ModbusPacketValidator>.Instance;
        _validator = new ModbusPacketValidator(validatorLogger);
    }

    /// <summary>
    /// Run all standard compliance tests
    /// </summary>
    /// <returns>Overall test result</returns>
    public async Task<TestSuiteResult> RunAllTestsAsync()
    {
        var result = new TestSuiteResult();
        
        _logger.LogInformation("Starting Modbus Standard Compliance Tests");
        
        // Test TCP packet compliance
        await RunTcpPacketTests(result);
        
        // Test RTU packet compliance
        await RunRtuPacketTests(result);
        
        // Test function code compliance
        await RunFunctionCodeTests(result);
        
        // Test data format compliance
        await RunDataFormatTests(result);
        
        // Test error handling compliance
        await RunErrorHandlingTests(result);
        
        _logger.LogInformation("Modbus Standard Compliance Tests completed. Passed: {Passed}/{Total}", 
            result.PassedTests, result.TotalTests);
        
        return result;
    }

    private async Task RunTcpPacketTests(TestSuiteResult result)
    {
        _logger.LogInformation("Running TCP Packet Compliance Tests");
        
        // Test 1: Basic TCP packet structure
        await TestBasicTcpPacketStructure(result);
        
        // Test 2: TCP header validation
        await TestTcpHeaderValidation(result);
        
        // Test 3: TCP packet size limits
        await TestTcpPacketSizeLimits(result);
        
        // Test 4: Transaction ID handling
        await TestTransactionIdHandling(result);
    }

    private async Task RunRtuPacketTests(TestSuiteResult result)
    {
        _logger.LogInformation("Running RTU Packet Compliance Tests");
        
        // Test 1: RTU CRC calculation
        await TestRtuCrcCalculation(result);
        
        // Test 2: RTU packet structure
        await TestRtuPacketStructure(result);
        
        // Test 3: RTU known test vectors
        await TestRtuKnownVectors(result);
    }

    private async Task RunFunctionCodeTests(TestSuiteResult result)
    {
        _logger.LogInformation("Running Function Code Compliance Tests");
        
        // Test all standard function codes
        var functionCodes = new byte[]
        {
            ModbusFunctionCodes.ReadCoils,
            ModbusFunctionCodes.ReadDiscreteInputs,
            ModbusFunctionCodes.ReadHoldingRegisters,
            ModbusFunctionCodes.ReadInputRegisters,
            ModbusFunctionCodes.WriteSingleCoil,
            ModbusFunctionCodes.WriteSingleRegister,
            ModbusFunctionCodes.WriteMultipleCoils,
            ModbusFunctionCodes.WriteMultipleRegisters
        };

        foreach (var functionCode in functionCodes)
        {
            await TestFunctionCodeCompliance(result, functionCode);
        }
    }

    private async Task RunDataFormatTests(TestSuiteResult result)
    {
        _logger.LogInformation("Running Data Format Compliance Tests");
        
        // Test 1: Read requests format
        await TestReadRequestFormats(result);
        
        // Test 2: Write requests format
        await TestWriteRequestFormats(result);
        
        // Test 3: Response parsing
        await TestResponseParsing(result);
    }

    private async Task RunErrorHandlingTests(TestSuiteResult result)
    {
        _logger.LogInformation("Running Error Handling Compliance Tests");
        
        // Test exception response format
        await TestExceptionResponseFormat(result);
        
        // Test all exception codes
        await TestExceptionCodes(result);
    }

    private async Task TestBasicTcpPacketStructure(TestSuiteResult result)
    {
        var testName = "TCP Basic Packet Structure";
        try
        {
            // Create a standard Read Holding Registers request
            var packet = ModbusRequestBuilder.CreateReadHoldingRegistersRequest(
                transactionId: 0x1234,
                unitId: 0x01,
                startAddress: 0x0000,
                quantity: 10
            );

            var validation = _validator.ValidateModbusTcpPacket(packet);
            
            if (validation.IsValid)
            {
                result.AddPassedTest(testName);
                _logger.LogInformation("✓ {TestName} - PASSED", testName);
            }
            else
            {
                result.AddFailedTest(testName, validation.ToString());
                _logger.LogWarning("✗ {TestName} - FAILED: {Issues}", testName, validation.ToString());
            }
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - EXCEPTION", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestTcpHeaderValidation(TestSuiteResult result)
    {
        var testName = "TCP Header Validation";
        try
        {
            var packet = new ModbusPacket(0x5678, 0x02, ModbusFunctionCodes.ReadCoils, new byte[] { 0x00, 0x01, 0x00, 0x08 });
            
            // Verify header fields
            var bytes = packet.ToByteArray();
            
            // Check Transaction ID (bytes 0-1, big endian)
            var txId = (ushort)((bytes[0] << 8) | bytes[1]);
            if (txId != 0x5678)
            {
                throw new Exception($"Transaction ID mismatch. Expected: 0x5678, Got: 0x{txId:X4}");
            }
            
            // Check Protocol ID (bytes 2-3, should be 0x0000)
            var protocolId = (ushort)((bytes[2] << 8) | bytes[3]);
            if (protocolId != 0x0000)
            {
                throw new Exception($"Protocol ID should be 0x0000, Got: 0x{protocolId:X4}");
            }
            
            // Check Length field (bytes 4-5)
            var length = (ushort)((bytes[4] << 8) | bytes[5]);
            var expectedLength = 1 + 1 + packet.Data.Length; // UnitId + FunctionCode + Data
            if (length != expectedLength)
            {
                throw new Exception($"Length field mismatch. Expected: {expectedLength}, Got: {length}");
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestTcpPacketSizeLimits(TestSuiteResult result)
    {
        var testName = "TCP Packet Size Limits";
        try
        {
            // Test minimum packet size (should work)
            var minPacket = new ModbusPacket(0x0001, 0x01, ModbusFunctionCodes.ReadCoils, new byte[] { 0x00, 0x00, 0x00, 0x01 });
            var minBytes = minPacket.ToByteArray();
            var parsedMin = ModbusPacket.Parse(minBytes);
            
            // Test maximum reasonable packet size
            var maxData = new byte[250]; // Reasonable max data size
            var maxPacket = new ModbusPacket(0x0002, 0x01, ModbusFunctionCodes.WriteMultipleRegisters, maxData);
            var maxBytes = maxPacket.ToByteArray();
            
            if (maxBytes.Length > 260) // Modbus TCP max packet size
            {
                throw new Exception($"Packet too large: {maxBytes.Length} bytes (max 260)");
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestTransactionIdHandling(TestSuiteResult result)
    {
        var testName = "Transaction ID Handling";
        try
        {
            // Test various transaction IDs
            var transactionIds = new ushort[] { 0x0000, 0x0001, 0x7FFF, 0x8000, 0xFFFF };
            
            foreach (var txId in transactionIds)
            {
                var packet = ModbusRequestBuilder.CreateReadHoldingRegistersRequest(txId, 0x01, 0x0000, 1);
                var bytes = packet.ToByteArray();
                var parsed = ModbusPacket.Parse(bytes);
                
                if (parsed.TransactionId != txId)
                {
                    throw new Exception($"Transaction ID round-trip failed for 0x{txId:X4}");
                }
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestRtuCrcCalculation(TestSuiteResult result)
    {
        var testName = "RTU CRC Calculation";
        try
        {
            // Test with known CRC values from Modbus specification
            // Example: Read Holding Registers, Slave 1, Address 0, Quantity 2
            var testData = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x02 };
            var expectedCrc = (ushort)0xC40B; // Known CRC for this data
            
            var calculatedCrc = ModbusRtuPacket.CalculateCrc16(testData);
            
            if (calculatedCrc != expectedCrc)
            {
                throw new Exception($"CRC calculation failed. Expected: 0x{expectedCrc:X4}, Got: 0x{calculatedCrc:X4}");
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestRtuPacketStructure(TestSuiteResult result)
    {
        var testName = "RTU Packet Structure";
        try
        {
            var packet = new ModbusRtuPacket(0x01, ModbusFunctionCodes.ReadHoldingRegisters, new byte[] { 0x00, 0x00, 0x00, 0x02 });
            var validation = _validator.ValidateModbusRtuPacket(packet);
            
            if (!validation.IsValid)
            {
                throw new Exception(validation.ToString());
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestRtuKnownVectors(TestSuiteResult result)
    {
        var testName = "RTU Known Test Vectors";
        try
        {
            // Known test vector from Modbus specification
            var knownPacket = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x02, 0x0B, 0xC4 };
            var parsed = ModbusRtuPacket.Parse(knownPacket);
            
            if (parsed.SlaveAddress != 0x01 || parsed.FunctionCode != 0x03)
            {
                throw new Exception("Failed to parse known test vector correctly");
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestFunctionCodeCompliance(TestSuiteResult result, byte functionCode)
    {
        var testName = $"Function Code 0x{functionCode:X2} Compliance";
        try
        {
            // Test that function code is recognized as valid
            if (!ModbusFunctionCodes.IsValidFunctionCode(functionCode))
            {
                throw new Exception($"Function code 0x{functionCode:X2} not recognized as valid");
            }
            
            // Test exception response creation
            var exceptionCode = ModbusFunctionCodes.CreateExceptionResponse(functionCode);
            if (!ModbusFunctionCodes.IsExceptionResponse(exceptionCode))
            {
                throw new Exception($"Exception response creation failed for function code 0x{functionCode:X2}");
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestReadRequestFormats(TestSuiteResult result)
    {
        var testName = "Read Request Formats";
        try
        {
            // Test Read Holding Registers format
            var packet = ModbusRequestBuilder.CreateReadHoldingRegistersRequest(0x0001, 0x01, 0x0000, 10);
            
            if (packet.Data.Length != 4)
            {
                throw new Exception("Read request should have 4 bytes of data");
            }
            
            // Verify address and quantity encoding (big endian)
            var address = (ushort)((packet.Data[0] << 8) | packet.Data[1]);
            var quantity = (ushort)((packet.Data[2] << 8) | packet.Data[3]);
            
            if (address != 0x0000 || quantity != 10)
            {
                throw new Exception("Address or quantity encoding incorrect");
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestWriteRequestFormats(TestSuiteResult result)
    {
        var testName = "Write Request Formats";
        try
        {
            // Test Write Single Register format
            var packet = ModbusRequestBuilder.CreateWriteSingleRegisterRequest(0x0001, 0x01, 0x0000, 0x1234);
            
            if (packet.Data.Length != 4)
            {
                throw new Exception("Write single register should have 4 bytes of data");
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestResponseParsing(TestSuiteResult result)
    {
        var testName = "Response Parsing";
        try
        {
            // Create a mock response for Read Holding Registers
            var responseData = new byte[] { 0x04, 0x12, 0x34, 0x56, 0x78 }; // Byte count + 2 registers
            var responsePacket = new ModbusPacket(0x0001, 0x01, ModbusFunctionCodes.ReadHoldingRegisters, responseData);
            
            var registers = ModbusResponseParser.ParseReadHoldingRegistersResponse(responsePacket, 2);
            
            if (registers.Length != 2 || registers[0] != 0x1234 || registers[1] != 0x5678)
            {
                throw new Exception("Response parsing failed");
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestExceptionResponseFormat(TestSuiteResult result)
    {
        var testName = "Exception Response Format";
        try
        {
            var originalPacket = ModbusRequestBuilder.CreateReadHoldingRegistersRequest(0x0001, 0x01, 0x0000, 1);
            var exceptionPacket = ModbusPacket.CreateExceptionResponse(originalPacket, ModbusExceptionCodes.IllegalDataAddress);
            
            if (!exceptionPacket.IsExceptionResponse())
            {
                throw new Exception("Exception response not properly formatted");
            }
            
            var exceptionCode = exceptionPacket.GetExceptionCode();
            if (exceptionCode != ModbusExceptionCodes.IllegalDataAddress)
            {
                throw new Exception("Exception code not preserved");
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }

    private async Task TestExceptionCodes(TestSuiteResult result)
    {
        var testName = "Exception Codes";
        try
        {
            var exceptionCodes = new byte[]
            {
                ModbusExceptionCodes.IllegalFunction,
                ModbusExceptionCodes.IllegalDataAddress,
                ModbusExceptionCodes.IllegalDataValue,
                ModbusExceptionCodes.SlaveDeviceFailure
            };

            foreach (var code in exceptionCodes)
            {
                if (!ModbusExceptionCodes.IsValidExceptionCode(code))
                {
                    throw new Exception($"Exception code 0x{code:X2} not recognized as valid");
                }
                
                var description = ModbusExceptionCodes.GetDescription(code);
                if (string.IsNullOrEmpty(description) || description.StartsWith("Unknown"))
                {
                    throw new Exception($"No description available for exception code 0x{code:X2}");
                }
            }
            
            result.AddPassedTest(testName);
            _logger.LogInformation("✓ {TestName} - PASSED", testName);
        }
        catch (Exception ex)
        {
            result.AddFailedTest(testName, ex.Message);
            _logger.LogError(ex, "✗ {TestName} - FAILED", testName);
        }
        
        await Task.CompletedTask;
    }
}

/// <summary>
/// Result of test suite execution
/// </summary>
public class TestSuiteResult
{
    public List<string> PassedTests { get; } = new();
    public List<(string TestName, string Error)> FailedTests { get; } = new();
    
    public int TotalTests => PassedTests.Count + FailedTests.Count;
    public int PassedTestsCount => PassedTests.Count;
    public int FailedTestsCount => FailedTests.Count;
    public bool AllTestsPassed => FailedTests.Count == 0;
    
    public void AddPassedTest(string testName)
    {
        PassedTests.Add(testName);
    }
    
    public void AddFailedTest(string testName, string error)
    {
        FailedTests.Add((testName, error));
    }
    
    public override string ToString()
    {
        var result = $"Test Suite Results: {PassedTestsCount}/{TotalTests} tests passed\n\n";
        
        if (PassedTests.Count > 0)
        {
            result += "✓ PASSED TESTS:\n";
            foreach (var test in PassedTests)
            {
                result += $"  • {test}\n";
            }
            result += "\n";
        }
        
        if (FailedTests.Count > 0)
        {
            result += "✗ FAILED TESTS:\n";
            foreach (var (testName, error) in FailedTests)
            {
                result += $"  • {testName}: {error}\n";
            }
        }
        
        return result;
    }
}
