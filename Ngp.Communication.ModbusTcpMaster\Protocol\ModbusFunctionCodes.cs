namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Modbus function codes as defined in the Modbus specification
/// </summary>
public static class ModbusFunctionCodes
{
    // Read Functions
    /// <summary>
    /// Read Coils (0x01) - Read 1 to 2000 contiguous status of coils
    /// </summary>
    public const byte ReadCoils = 0x01;

    /// <summary>
    /// Read Discrete Inputs (0x02) - Read 1 to 2000 contiguous status of discrete inputs
    /// </summary>
    public const byte ReadDiscreteInputs = 0x02;

    /// <summary>
    /// Read Holding Registers (0x03) - Read 1 to 125 contiguous holding registers
    /// </summary>
    public const byte ReadHoldingRegisters = 0x03;

    /// <summary>
    /// Read Input Registers (0x04) - Read 1 to 125 contiguous input registers
    /// </summary>
    public const byte ReadInputRegisters = 0x04;

    // Write Functions
    /// <summary>
    /// Write Single Coil (0x05) - Write a single output to either ON or OFF
    /// </summary>
    public const byte WriteSingleCoil = 0x05;

    /// <summary>
    /// Write Single Register (0x06) - Write a single holding register
    /// </summary>
    public const byte WriteSingleRegister = 0x06;

    /// <summary>
    /// Write Multiple Coils (0x0F) - Write each coil in a sequence of coils to either ON or OFF
    /// </summary>
    public const byte WriteMultipleCoils = 0x0F;

    /// <summary>
    /// Write Multiple Registers (0x10) - Write a block of contiguous registers
    /// </summary>
    public const byte WriteMultipleRegisters = 0x10;

    // Diagnostic Functions
    /// <summary>
    /// Read Exception Status (0x07) - Read the contents of eight Exception Status outputs
    /// </summary>
    public const byte ReadExceptionStatus = 0x07;

    /// <summary>
    /// Diagnostics (0x08) - Perform a series of tests on the communication system
    /// </summary>
    public const byte Diagnostics = 0x08;

    /// <summary>
    /// Get Comm Event Counter (0x0B) - Get a status word and an event count
    /// </summary>
    public const byte GetCommEventCounter = 0x0B;

    /// <summary>
    /// Get Comm Event Log (0x0C) - Get a status word, event count, message count, and a field of event bytes
    /// </summary>
    public const byte GetCommEventLog = 0x0C;

    // Advanced Functions
    /// <summary>
    /// Read Write Multiple Registers (0x17) - Perform a combination of one read operation and one write operation
    /// </summary>
    public const byte ReadWriteMultipleRegisters = 0x17;

    /// <summary>
    /// Mask Write Register (0x16) - Modify the contents of a specified holding register using a combination of an AND mask, an OR mask, and the register's current contents
    /// </summary>
    public const byte MaskWriteRegister = 0x16;

    /// <summary>
    /// Read FIFO Queue (0x18) - Read the contents of a First-In-First-Out (FIFO) queue of register
    /// </summary>
    public const byte ReadFifoQueue = 0x18;

    /// <summary>
    /// Encapsulated Interface Transport (0x2B) - Tunneling service which can be used to access various kinds of remote devices
    /// </summary>
    public const byte EncapsulatedInterfaceTransport = 0x2B;

    // Error flag - when set, indicates an exception response
    /// <summary>
    /// Exception response flag (0x80) - Added to function code to indicate exception response
    /// </summary>
    public const byte ExceptionFlag = 0x80;

    /// <summary>
    /// Check if a function code represents an exception response
    /// </summary>
    /// <param name="functionCode">Function code to check</param>
    /// <returns>True if the function code indicates an exception response</returns>
    public static bool IsExceptionResponse(byte functionCode)
    {
        return (functionCode & ExceptionFlag) == ExceptionFlag;
    }

    /// <summary>
    /// Get the original function code from an exception response
    /// </summary>
    /// <param name="exceptionFunctionCode">Exception function code</param>
    /// <returns>Original function code</returns>
    public static byte GetOriginalFunctionCode(byte exceptionFunctionCode)
    {
        return (byte)(exceptionFunctionCode & ~ExceptionFlag);
    }

    /// <summary>
    /// Create an exception response function code
    /// </summary>
    /// <param name="originalFunctionCode">Original function code</param>
    /// <returns>Exception response function code</returns>
    public static byte CreateExceptionResponse(byte originalFunctionCode)
    {
        return (byte)(originalFunctionCode | ExceptionFlag);
    }

    /// <summary>
    /// Validate if a function code is supported
    /// </summary>
    /// <param name="functionCode">Function code to validate</param>
    /// <returns>True if the function code is supported</returns>
    public static bool IsValidFunctionCode(byte functionCode)
    {
        // Remove exception flag for validation
        var code = (byte)(functionCode & ~ExceptionFlag);
        
        return code switch
        {
            ReadCoils or ReadDiscreteInputs or ReadHoldingRegisters or ReadInputRegisters or
            WriteSingleCoil or WriteSingleRegister or WriteMultipleCoils or WriteMultipleRegisters or
            ReadExceptionStatus or Diagnostics or GetCommEventCounter or GetCommEventLog or
            MaskWriteRegister or ReadWriteMultipleRegisters or ReadFifoQueue or
            EncapsulatedInterfaceTransport => true,
            _ => false
        };
    }
}
