using System.Buffers.Binary;

namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Builder class for creating standard-compliant Modbus request packets
/// Ensures all requests follow Modbus specification exactly
/// </summary>
public static class ModbusRequestBuilder
{
    // Modbus register limits as per specification
    private const ushort MaxCoilsPerRead = 2000;
    private const ushort MaxDiscreteInputsPerRead = 2000;
    private const ushort MaxHoldingRegistersPerRead = 125;
    private const ushort MaxInputRegistersPerRead = 125;
    private const ushort MaxCoilsPerWrite = 1968;
    private const ushort MaxRegistersPerWrite = 123;

    /// <summary>
    /// Create a Read Coils request (Function Code 0x01)
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of coils to read</param>
    /// <returns>Modbus TCP packet for Read Coils request</returns>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when parameters are out of valid range</exception>
    public static ModbusPacket CreateReadCoilsRequest(ushort transactionId, byte unitId, ushort startAddress, ushort quantity)
    {
        ValidateQuantity(quantity, 1, MaxCoilsPerRead, "coils");
        
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0, 2), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2, 2), quantity);
        
        return new ModbusPacket(transactionId, unitId, ModbusFunctionCodes.ReadCoils, data);
    }

    /// <summary>
    /// Create a Read Discrete Inputs request (Function Code 0x02)
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of discrete inputs to read</param>
    /// <returns>Modbus TCP packet for Read Discrete Inputs request</returns>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when parameters are out of valid range</exception>
    public static ModbusPacket CreateReadDiscreteInputsRequest(ushort transactionId, byte unitId, ushort startAddress, ushort quantity)
    {
        ValidateQuantity(quantity, 1, MaxDiscreteInputsPerRead, "discrete inputs");
        
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0, 2), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2, 2), quantity);
        
        return new ModbusPacket(transactionId, unitId, ModbusFunctionCodes.ReadDiscreteInputs, data);
    }

    /// <summary>
    /// Create a Read Holding Registers request (Function Code 0x03)
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of holding registers to read</param>
    /// <returns>Modbus TCP packet for Read Holding Registers request</returns>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when parameters are out of valid range</exception>
    public static ModbusPacket CreateReadHoldingRegistersRequest(ushort transactionId, byte unitId, ushort startAddress, ushort quantity)
    {
        ValidateQuantity(quantity, 1, MaxHoldingRegistersPerRead, "holding registers");
        
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0, 2), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2, 2), quantity);
        
        return new ModbusPacket(transactionId, unitId, ModbusFunctionCodes.ReadHoldingRegisters, data);
    }

    /// <summary>
    /// Create a Read Input Registers request (Function Code 0x04)
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="quantity">Number of input registers to read</param>
    /// <returns>Modbus TCP packet for Read Input Registers request</returns>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when parameters are out of valid range</exception>
    public static ModbusPacket CreateReadInputRegistersRequest(ushort transactionId, byte unitId, ushort startAddress, ushort quantity)
    {
        ValidateQuantity(quantity, 1, MaxInputRegistersPerRead, "input registers");
        
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0, 2), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2, 2), quantity);
        
        return new ModbusPacket(transactionId, unitId, ModbusFunctionCodes.ReadInputRegisters, data);
    }

    /// <summary>
    /// Create a Write Single Coil request (Function Code 0x05)
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="address">Coil address</param>
    /// <param name="value">Coil value (true = ON, false = OFF)</param>
    /// <returns>Modbus TCP packet for Write Single Coil request</returns>
    public static ModbusPacket CreateWriteSingleCoilRequest(ushort transactionId, byte unitId, ushort address, bool value)
    {
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0, 2), address);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2, 2), (ushort)(value ? 0xFF00 : 0x0000));
        
        return new ModbusPacket(transactionId, unitId, ModbusFunctionCodes.WriteSingleCoil, data);
    }

    /// <summary>
    /// Create a Write Single Register request (Function Code 0x06)
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="address">Register address</param>
    /// <param name="value">Register value</param>
    /// <returns>Modbus TCP packet for Write Single Register request</returns>
    public static ModbusPacket CreateWriteSingleRegisterRequest(ushort transactionId, byte unitId, ushort address, ushort value)
    {
        var data = new byte[4];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0, 2), address);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2, 2), value);
        
        return new ModbusPacket(transactionId, unitId, ModbusFunctionCodes.WriteSingleRegister, data);
    }

    /// <summary>
    /// Create a Write Multiple Coils request (Function Code 0x0F)
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Coil values</param>
    /// <returns>Modbus TCP packet for Write Multiple Coils request</returns>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when parameters are out of valid range</exception>
    public static ModbusPacket CreateWriteMultipleCoilsRequest(ushort transactionId, byte unitId, ushort startAddress, bool[] values)
    {
        if (values == null || values.Length == 0)
            throw new ArgumentException("Values array cannot be null or empty");
        
        ValidateQuantity((ushort)values.Length, 1, MaxCoilsPerWrite, "coils");
        
        var byteCount = (byte)((values.Length + 7) / 8); // Round up to nearest byte
        var data = new byte[5 + byteCount];
        
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0, 2), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2, 2), (ushort)values.Length);
        data[4] = byteCount;
        
        // Pack coil values into bytes
        for (int i = 0; i < values.Length; i++)
        {
            if (values[i])
            {
                var byteIndex = 5 + (i / 8);
                var bitIndex = i % 8;
                data[byteIndex] |= (byte)(1 << bitIndex);
            }
        }
        
        return new ModbusPacket(transactionId, unitId, ModbusFunctionCodes.WriteMultipleCoils, data);
    }

    /// <summary>
    /// Create a Write Multiple Registers request (Function Code 0x10)
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Register values</param>
    /// <returns>Modbus TCP packet for Write Multiple Registers request</returns>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when parameters are out of valid range</exception>
    public static ModbusPacket CreateWriteMultipleRegistersRequest(ushort transactionId, byte unitId, ushort startAddress, ushort[] values)
    {
        if (values == null || values.Length == 0)
            throw new ArgumentException("Values array cannot be null or empty");
        
        ValidateQuantity((ushort)values.Length, 1, MaxRegistersPerWrite, "registers");
        
        var byteCount = (byte)(values.Length * 2);
        var data = new byte[5 + byteCount];
        
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0, 2), startAddress);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2, 2), (ushort)values.Length);
        data[4] = byteCount;
        
        // Pack register values
        for (int i = 0; i < values.Length; i++)
        {
            BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(5 + (i * 2), 2), values[i]);
        }
        
        return new ModbusPacket(transactionId, unitId, ModbusFunctionCodes.WriteMultipleRegisters, data);
    }

    /// <summary>
    /// Create a Mask Write Register request (Function Code 0x16)
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="address">Register address</param>
    /// <param name="andMask">AND mask</param>
    /// <param name="orMask">OR mask</param>
    /// <returns>Modbus TCP packet for Mask Write Register request</returns>
    public static ModbusPacket CreateMaskWriteRegisterRequest(ushort transactionId, byte unitId, ushort address, ushort andMask, ushort orMask)
    {
        var data = new byte[6];
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(0, 2), address);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(2, 2), andMask);
        BinaryPrimitives.WriteUInt16BigEndian(data.AsSpan(4, 2), orMask);
        
        return new ModbusPacket(transactionId, unitId, ModbusFunctionCodes.MaskWriteRegister, data);
    }

    /// <summary>
    /// Validate quantity parameter against Modbus specification limits
    /// </summary>
    /// <param name="quantity">Quantity to validate</param>
    /// <param name="min">Minimum allowed value</param>
    /// <param name="max">Maximum allowed value</param>
    /// <param name="itemType">Type of item for error message</param>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when quantity is out of range</exception>
    private static void ValidateQuantity(ushort quantity, ushort min, ushort max, string itemType)
    {
        if (quantity < min || quantity > max)
        {
            throw new ArgumentOutOfRangeException(nameof(quantity), 
                $"Quantity of {itemType} must be between {min} and {max}. Received: {quantity}");
        }
    }
}
