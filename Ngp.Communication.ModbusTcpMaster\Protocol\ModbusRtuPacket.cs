namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Represents a Modbus RTU over TCP packet with CRC validation
/// Ensures compliance with Modbus RTU specification when used over TCP
/// </summary>
public class ModbusRtuPacket
{
    private const int MinRtuPacketLength = 4; // SlaveId + FunctionCode + CRC (2 bytes)
    private const int MaxRtuPacketLength = 256; // As per Modbus RTU specification
    private const int CrcLength = 2;

    /// <summary>
    /// Slave address (Unit ID)
    /// </summary>
    public byte SlaveAddress { get; set; }

    /// <summary>
    /// Function code
    /// </summary>
    public byte FunctionCode { get; set; }

    /// <summary>
    /// Data portion of the packet (without CRC)
    /// </summary>
    public byte[] Data { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// CRC-16 checksum
    /// </summary>
    public ushort Crc { get; set; }

    /// <summary>
    /// Create a new Modbus RTU packet
    /// </summary>
    public ModbusRtuPacket()
    {
    }

    /// <summary>
    /// Create a new Modbus RTU packet with specified parameters
    /// </summary>
    /// <param name="slaveAddress">Slave address</param>
    /// <param name="functionCode">Function code</param>
    /// <param name="data">Data bytes</param>
    public ModbusRtuPacket(byte slaveAddress, byte functionCode, byte[]? data = null)
    {
        SlaveAddress = slaveAddress;
        FunctionCode = functionCode;
        Data = data ?? Array.Empty<byte>();
        Crc = CalculateCrc();
    }

    /// <summary>
    /// Serialize the packet to byte array for transmission
    /// Includes proper CRC calculation according to Modbus RTU specification
    /// </summary>
    /// <returns>Serialized packet bytes</returns>
    public byte[] ToByteArray()
    {
        var totalLength = 1 + 1 + Data.Length + CrcLength; // SlaveAddress + FunctionCode + Data + CRC
        var buffer = new byte[totalLength];

        buffer[0] = SlaveAddress;
        buffer[1] = FunctionCode;

        if (Data.Length > 0)
        {
            Data.CopyTo(buffer, 2);
        }

        // Calculate and append CRC
        var crc = CalculateCrc16(buffer.AsSpan(0, buffer.Length - CrcLength));
        buffer[^2] = (byte)(crc & 0xFF);        // CRC Low byte
        buffer[^1] = (byte)((crc >> 8) & 0xFF); // CRC High byte

        return buffer;
    }

    /// <summary>
    /// Parse a byte array into a Modbus RTU packet
    /// Validates CRC and packet format according to Modbus RTU specification
    /// </summary>
    /// <param name="buffer">Buffer containing packet data</param>
    /// <returns>Parsed Modbus RTU packet</returns>
    /// <exception cref="ArgumentException">Thrown when packet format is invalid</exception>
    /// <exception cref="InvalidOperationException">Thrown when CRC validation fails</exception>
    public static ModbusRtuPacket Parse(byte[] buffer)
    {
        return Parse(buffer.AsSpan());
    }

    /// <summary>
    /// Parse a span of bytes into a Modbus RTU packet
    /// Validates CRC and packet format according to Modbus RTU specification
    /// </summary>
    /// <param name="buffer">Span containing packet data</param>
    /// <returns>Parsed Modbus RTU packet</returns>
    /// <exception cref="ArgumentException">Thrown when packet format is invalid</exception>
    /// <exception cref="InvalidOperationException">Thrown when CRC validation fails</exception>
    public static ModbusRtuPacket Parse(ReadOnlySpan<byte> buffer)
    {
        // Validate minimum packet length
        if (buffer.Length < MinRtuPacketLength)
        {
            throw new ArgumentException($"RTU packet too short. Minimum length is {MinRtuPacketLength} bytes, received {buffer.Length} bytes.");
        }

        // Validate maximum packet length
        if (buffer.Length > MaxRtuPacketLength)
        {
            throw new ArgumentException($"RTU packet too long. Maximum length is {MaxRtuPacketLength} bytes, received {buffer.Length} bytes.");
        }

        // Extract components
        var slaveAddress = buffer[0];
        var functionCode = buffer[1];
        var dataLength = buffer.Length - 2 - CrcLength; // Total - SlaveAddress - FunctionCode - CRC
        var data = dataLength > 0 ? buffer[2..(2 + dataLength)].ToArray() : Array.Empty<byte>();

        // Extract CRC (Little Endian in RTU)
        var crcLow = buffer[^2];
        var crcHigh = buffer[^1];
        var receivedCrc = (ushort)(crcLow | (crcHigh << 8));

        // Validate CRC
        var calculatedCrc = CalculateCrc16(buffer[0..^CrcLength]);
        if (receivedCrc != calculatedCrc)
        {
            throw new InvalidOperationException($"CRC validation failed. Expected: 0x{calculatedCrc:X4}, Received: 0x{receivedCrc:X4}");
        }

        // Validate function code
        if (!ModbusFunctionCodes.IsValidFunctionCode(functionCode))
        {
            throw new ArgumentException($"Invalid function code: 0x{functionCode:X2}");
        }

        return new ModbusRtuPacket
        {
            SlaveAddress = slaveAddress,
            FunctionCode = functionCode,
            Data = data,
            Crc = receivedCrc
        };
    }

    /// <summary>
    /// Calculate CRC for the current packet data
    /// </summary>
    /// <returns>Calculated CRC-16</returns>
    public ushort CalculateCrc()
    {
        var totalLength = 1 + 1 + Data.Length; // SlaveAddress + FunctionCode + Data
        var buffer = new byte[totalLength];
        
        buffer[0] = SlaveAddress;
        buffer[1] = FunctionCode;
        
        if (Data.Length > 0)
        {
            Data.CopyTo(buffer, 2);
        }

        return CalculateCrc16(buffer);
    }

    /// <summary>
    /// Calculate CRC-16 for Modbus RTU (polynomial 0xA001)
    /// Implementation follows Modbus specification exactly
    /// </summary>
    /// <param name="data">Data to calculate CRC for</param>
    /// <returns>CRC-16 value</returns>
    public static ushort CalculateCrc16(ReadOnlySpan<byte> data)
    {
        const ushort polynomial = 0xA001; // Modbus CRC-16 polynomial
        ushort crc = 0xFFFF; // Initial value

        foreach (var b in data)
        {
            crc ^= b;
            
            for (int i = 0; i < 8; i++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= polynomial;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }

        return crc;
    }

    /// <summary>
    /// Validate CRC of the current packet
    /// </summary>
    /// <returns>True if CRC is valid</returns>
    public bool ValidateCrc()
    {
        var calculatedCrc = CalculateCrc();
        return Crc == calculatedCrc;
    }

    /// <summary>
    /// Check if this packet is an exception response
    /// </summary>
    /// <returns>True if this is an exception response</returns>
    public bool IsExceptionResponse()
    {
        return ModbusFunctionCodes.IsExceptionResponse(FunctionCode);
    }

    /// <summary>
    /// Get the exception code if this is an exception response
    /// </summary>
    /// <returns>Exception code, or null if not an exception response</returns>
    public byte? GetExceptionCode()
    {
        if (IsExceptionResponse() && Data.Length > 0)
        {
            return Data[0];
        }
        return null;
    }

    /// <summary>
    /// Create an exception response packet
    /// </summary>
    /// <param name="originalPacket">Original request packet</param>
    /// <param name="exceptionCode">Exception code to return</param>
    /// <returns>Exception response packet</returns>
    public static ModbusRtuPacket CreateExceptionResponse(ModbusRtuPacket originalPacket, byte exceptionCode)
    {
        var exceptionFunctionCode = ModbusFunctionCodes.CreateExceptionResponse(originalPacket.FunctionCode);
        return new ModbusRtuPacket(
            originalPacket.SlaveAddress,
            exceptionFunctionCode,
            new[] { exceptionCode }
        );
    }

    /// <summary>
    /// Validate packet integrity and format
    /// </summary>
    /// <returns>True if packet is valid</returns>
    public bool IsValid()
    {
        try
        {
            // Check function code validity
            if (!ModbusFunctionCodes.IsValidFunctionCode(FunctionCode))
                return false;

            // Validate CRC
            if (!ValidateCrc())
                return false;

            // Additional validation for exception responses
            if (IsExceptionResponse())
            {
                if (Data.Length != 1)
                    return false;

                if (!ModbusExceptionCodes.IsValidExceptionCode(Data[0]))
                    return false;
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Get a string representation of the packet for debugging
    /// </summary>
    /// <returns>String representation of the packet</returns>
    public override string ToString()
    {
        var dataHex = Data.Length > 0 ? Convert.ToHexString(Data) : "None";
        var exceptionInfo = IsExceptionResponse() ? $" (Exception: {GetExceptionCode()})" : "";
        var crcValid = ValidateCrc() ? "Valid" : "Invalid";
        
        return $"ModbusRtuPacket [Slave: {SlaveAddress}, Func: 0x{FunctionCode:X2}{exceptionInfo}, Data: {dataHex}, CRC: 0x{Crc:X4} ({crcValid})]";
    }
}
