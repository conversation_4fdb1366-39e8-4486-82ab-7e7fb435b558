using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Protocol;
using Ngp.Communication.ModbusTcpMaster.Testing;

namespace Ngp.Communication.ModbusTcpMaster.Tests;

/// <summary>
/// Test program to validate Modbus packet compliance with official standards
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        // Setup logging
        var services = new ServiceCollection();
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var testLogger = serviceProvider.GetRequiredService<ILogger<StandardComplianceTests>>();

        logger.LogInformation("=== Modbus Standard Compliance Validation ===");
        logger.LogInformation("Testing Modbus TCP and RTU packet compliance with official specifications");
        logger.LogInformation("");

        try
        {
            // Run comprehensive compliance tests
            var complianceTests = new StandardComplianceTests(testLogger);
            var testResults = await complianceTests.RunAllTestsAsync();

            // Display results
            Console.WriteLine();
            Console.WriteLine("=== TEST RESULTS ===");
            Console.WriteLine(testResults.ToString());

            if (testResults.AllTestsPassed)
            {
                logger.LogInformation("🎉 ALL TESTS PASSED - Modbus implementation complies with official standards!");
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("✓ COMPLIANCE VERIFIED: Your Modbus implementation follows the official specification");
                Console.ResetColor();
            }
            else
            {
                logger.LogWarning("⚠️  Some tests failed - Please review the implementation");
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine("✗ COMPLIANCE ISSUES FOUND: Please address the failed tests");
                Console.ResetColor();
            }

            // Additional manual tests
            Console.WriteLine();
            Console.WriteLine("=== MANUAL VERIFICATION EXAMPLES ===");
            await RunManualVerificationExamples(logger);

        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Test execution failed");
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"ERROR: {ex.Message}");
            Console.ResetColor();
        }

        Console.WriteLine();
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }

    static async Task RunManualVerificationExamples(ILogger logger)
    {
        logger.LogInformation("Running manual verification examples...");

        // Example 1: Create and validate a standard Read Holding Registers request
        Console.WriteLine("\n1. Read Holding Registers Request (Function Code 0x03):");
        var readRequest = ModbusRequestBuilder.CreateReadHoldingRegistersRequest(
            transactionId: 0x0001,
            unitId: 0x01,
            startAddress: 0x0000,
            quantity: 10
        );

        var readBytes = readRequest.ToByteArray();
        Console.WriteLine($"   Packet bytes: {Convert.ToHexString(readBytes)}");
        Console.WriteLine($"   Transaction ID: 0x{readRequest.TransactionId:X4}");
        Console.WriteLine($"   Protocol ID: 0x{readRequest.ProtocolId:X4}");
        Console.WriteLine($"   Length: {readRequest.Length}");
        Console.WriteLine($"   Unit ID: 0x{readRequest.UnitId:X2}");
        Console.WriteLine($"   Function Code: 0x{readRequest.FunctionCode:X2}");
        Console.WriteLine($"   Data: {Convert.ToHexString(readRequest.Data)}");

        // Example 2: Create and validate a Write Single Register request
        Console.WriteLine("\n2. Write Single Register Request (Function Code 0x06):");
        var writeRequest = ModbusRequestBuilder.CreateWriteSingleRegisterRequest(
            transactionId: 0x0002,
            unitId: 0x01,
            address: 0x0001,
            value: 0x1234
        );

        var writeBytes = writeRequest.ToByteArray();
        Console.WriteLine($"   Packet bytes: {Convert.ToHexString(writeBytes)}");
        Console.WriteLine($"   Writing value 0x1234 to register 0x0001");

        // Example 3: Create and validate an RTU packet with CRC
        Console.WriteLine("\n3. RTU Packet with CRC (Function Code 0x03):");
        var rtuPacket = new ModbusRtuPacket(
            slaveAddress: 0x01,
            functionCode: ModbusFunctionCodes.ReadHoldingRegisters,
            data: new byte[] { 0x00, 0x00, 0x00, 0x02 } // Start address 0, quantity 2
        );

        var rtuBytes = rtuPacket.ToByteArray();
        Console.WriteLine($"   RTU packet bytes: {Convert.ToHexString(rtuBytes)}");
        Console.WriteLine($"   Slave Address: 0x{rtuPacket.SlaveAddress:X2}");
        Console.WriteLine($"   Function Code: 0x{rtuPacket.FunctionCode:X2}");
        Console.WriteLine($"   Data: {Convert.ToHexString(rtuPacket.Data)}");
        Console.WriteLine($"   CRC: 0x{rtuPacket.Crc:X4}");
        Console.WriteLine($"   CRC Valid: {rtuPacket.ValidateCrc()}");

        // Example 4: Exception response
        Console.WriteLine("\n4. Exception Response (Illegal Data Address):");
        var exceptionResponse = ModbusPacket.CreateExceptionResponse(
            readRequest,
            ModbusExceptionCodes.IllegalDataAddress
        );

        var exceptionBytes = exceptionResponse.ToByteArray();
        Console.WriteLine($"   Exception packet bytes: {Convert.ToHexString(exceptionBytes)}");
        Console.WriteLine($"   Function Code: 0x{exceptionResponse.FunctionCode:X2} (Exception flag set)");
        Console.WriteLine($"   Exception Code: 0x{exceptionResponse.GetExceptionCode():X2}");
        Console.WriteLine($"   Description: {ModbusExceptionCodes.GetDescription(exceptionResponse.GetExceptionCode() ?? 0)}");

        // Example 5: Packet parsing verification
        Console.WriteLine("\n5. Packet Parsing Verification:");
        try
        {
            var parsedPacket = ModbusPacket.Parse(readBytes);
            Console.WriteLine($"   Original and parsed packets match: {parsedPacket.TransactionId == readRequest.TransactionId}");
            Console.WriteLine($"   Round-trip successful: ✓");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   Round-trip failed: {ex.Message}");
        }

        // Example 6: Known test vector validation (RTU)
        Console.WriteLine("\n6. Known Test Vector Validation (RTU):");
        try
        {
            // This is a known good RTU packet from Modbus specification
            var knownRtuBytes = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x02, 0x0B, 0xC4 };
            var parsedRtu = ModbusRtuPacket.Parse(knownRtuBytes);
            Console.WriteLine($"   Known vector parsed successfully: ✓");
            Console.WriteLine($"   Slave: {parsedRtu.SlaveAddress}, Function: 0x{parsedRtu.FunctionCode:X2}, CRC: 0x{parsedRtu.Crc:X4}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   Known vector parsing failed: {ex.Message}");
        }

        await Task.CompletedTask;
    }
}
