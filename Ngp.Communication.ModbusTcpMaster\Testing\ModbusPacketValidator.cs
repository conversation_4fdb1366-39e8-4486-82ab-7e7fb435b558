using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Protocol;

namespace Ngp.Communication.ModbusTcpMaster.Testing;

/// <summary>
/// Validator for ensuring Modbus packets comply with the official Modbus specification
/// </summary>
public class ModbusPacketValidator
{
    private readonly ILogger<ModbusPacketValidator> _logger;

    public ModbusPacketValidator(ILogger<ModbusPacketValidator> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Comprehensive validation of Modbus TCP packet compliance
    /// </summary>
    /// <param name="packet">Packet to validate</param>
    /// <returns>Validation result with details</returns>
    public ValidationResult ValidateModbusTcpPacket(ModbusPacket packet)
    {
        var result = new ValidationResult();
        
        try
        {
            // Test 1: Basic packet structure validation
            ValidateBasicStructure(packet, result);
            
            // Test 2: Header validation
            ValidateHeader(packet, result);
            
            // Test 3: Function code validation
            ValidateFunctionCode(packet, result);
            
            // Test 4: Data length validation
            ValidateDataLength(packet, result);
            
            // Test 5: Serialization/Deserialization round-trip test
            ValidateRoundTrip(packet, result);
            
            _logger.LogInformation("Modbus TCP packet validation completed. Valid: {IsValid}, Issues: {IssueCount}", 
                result.IsValid, result.Issues.Count);
        }
        catch (Exception ex)
        {
            result.Issues.Add($"Validation failed with exception: {ex.Message}");
            _logger.LogError(ex, "Exception during Modbus TCP packet validation");
        }
        
        return result;
    }

    /// <summary>
    /// Comprehensive validation of Modbus RTU packet compliance
    /// </summary>
    /// <param name="packet">RTU packet to validate</param>
    /// <returns>Validation result with details</returns>
    public ValidationResult ValidateModbusRtuPacket(ModbusRtuPacket packet)
    {
        var result = new ValidationResult();
        
        try
        {
            // Test 1: Basic RTU structure validation
            ValidateRtuBasicStructure(packet, result);
            
            // Test 2: CRC validation
            ValidateRtuCrc(packet, result);
            
            // Test 3: Function code validation
            ValidateRtuFunctionCode(packet, result);
            
            // Test 4: Serialization/Deserialization round-trip test
            ValidateRtuRoundTrip(packet, result);
            
            _logger.LogInformation("Modbus RTU packet validation completed. Valid: {IsValid}, Issues: {IssueCount}", 
                result.IsValid, result.Issues.Count);
        }
        catch (Exception ex)
        {
            result.Issues.Add($"RTU validation failed with exception: {ex.Message}");
            _logger.LogError(ex, "Exception during Modbus RTU packet validation");
        }
        
        return result;
    }

    private void ValidateBasicStructure(ModbusPacket packet, ValidationResult result)
    {
        if (packet == null)
        {
            result.Issues.Add("Packet is null");
            return;
        }

        if (!packet.IsValid())
        {
            result.Issues.Add("Packet basic validation failed");
        }
    }

    private void ValidateHeader(ModbusPacket packet, ValidationResult result)
    {
        // Protocol ID must be 0 for Modbus TCP
        if (packet.ProtocolId != 0)
        {
            result.Issues.Add($"Invalid Protocol ID. Expected: 0, Got: {packet.ProtocolId}");
        }

        // Length field validation
        var expectedLength = 1 + 1 + packet.Data.Length; // UnitId + FunctionCode + Data
        if (packet.Length != expectedLength)
        {
            result.Issues.Add($"Invalid Length field. Expected: {expectedLength}, Got: {packet.Length}");
        }

        // Transaction ID should be reasonable (not validating specific values as they're application-dependent)
        _logger.LogDebug("Transaction ID: {TransactionId}", packet.TransactionId);
    }

    private void ValidateFunctionCode(ModbusPacket packet, ValidationResult result)
    {
        if (!ModbusFunctionCodes.IsValidFunctionCode(packet.FunctionCode))
        {
            result.Issues.Add($"Invalid function code: 0x{packet.FunctionCode:X2}");
        }

        // Validate exception responses
        if (packet.IsExceptionResponse())
        {
            if (packet.Data.Length != 1)
            {
                result.Issues.Add("Exception response must contain exactly 1 byte of data (exception code)");
            }
            else if (!ModbusExceptionCodes.IsValidExceptionCode(packet.Data[0]))
            {
                result.Issues.Add($"Invalid exception code: 0x{packet.Data[0]:X2}");
            }
        }
    }

    private void ValidateDataLength(ModbusPacket packet, ValidationResult result)
    {
        // Validate data length based on function code
        switch (packet.FunctionCode)
        {
            case ModbusFunctionCodes.ReadCoils:
            case ModbusFunctionCodes.ReadDiscreteInputs:
            case ModbusFunctionCodes.ReadHoldingRegisters:
            case ModbusFunctionCodes.ReadInputRegisters:
                if (packet.Data.Length != 4)
                {
                    result.Issues.Add($"Read function data length should be 4 bytes, got {packet.Data.Length}");
                }
                break;
                
            case ModbusFunctionCodes.WriteSingleCoil:
            case ModbusFunctionCodes.WriteSingleRegister:
                if (packet.Data.Length != 4)
                {
                    result.Issues.Add($"Single write function data length should be 4 bytes, got {packet.Data.Length}");
                }
                break;
        }
    }

    private void ValidateRoundTrip(ModbusPacket packet, ValidationResult result)
    {
        try
        {
            // Serialize to bytes
            var bytes = packet.ToByteArray();
            
            // Parse back from bytes
            var parsedPacket = ModbusPacket.Parse(bytes);
            
            // Compare key fields
            if (parsedPacket.TransactionId != packet.TransactionId)
            {
                result.Issues.Add("Round-trip failed: Transaction ID mismatch");
            }
            
            if (parsedPacket.ProtocolId != packet.ProtocolId)
            {
                result.Issues.Add("Round-trip failed: Protocol ID mismatch");
            }
            
            if (parsedPacket.Length != packet.Length)
            {
                result.Issues.Add("Round-trip failed: Length mismatch");
            }
            
            if (parsedPacket.UnitId != packet.UnitId)
            {
                result.Issues.Add("Round-trip failed: Unit ID mismatch");
            }
            
            if (parsedPacket.FunctionCode != packet.FunctionCode)
            {
                result.Issues.Add("Round-trip failed: Function Code mismatch");
            }
            
            if (!parsedPacket.Data.SequenceEqual(packet.Data))
            {
                result.Issues.Add("Round-trip failed: Data mismatch");
            }
        }
        catch (Exception ex)
        {
            result.Issues.Add($"Round-trip test failed: {ex.Message}");
        }
    }

    private void ValidateRtuBasicStructure(ModbusRtuPacket packet, ValidationResult result)
    {
        if (packet == null)
        {
            result.Issues.Add("RTU packet is null");
            return;
        }

        if (!packet.IsValid())
        {
            result.Issues.Add("RTU packet basic validation failed");
        }
    }

    private void ValidateRtuCrc(ModbusRtuPacket packet, ValidationResult result)
    {
        if (!packet.ValidateCrc())
        {
            result.Issues.Add("RTU packet CRC validation failed");
        }

        // Test CRC calculation consistency
        var calculatedCrc = packet.CalculateCrc();
        if (packet.Crc != calculatedCrc)
        {
            result.Issues.Add($"CRC mismatch. Stored: 0x{packet.Crc:X4}, Calculated: 0x{calculatedCrc:X4}");
        }
    }

    private void ValidateRtuFunctionCode(ModbusRtuPacket packet, ValidationResult result)
    {
        if (!ModbusFunctionCodes.IsValidFunctionCode(packet.FunctionCode))
        {
            result.Issues.Add($"Invalid RTU function code: 0x{packet.FunctionCode:X2}");
        }
    }

    private void ValidateRtuRoundTrip(ModbusRtuPacket packet, ValidationResult result)
    {
        try
        {
            // Serialize to bytes
            var bytes = packet.ToByteArray();
            
            // Parse back from bytes
            var parsedPacket = ModbusRtuPacket.Parse(bytes);
            
            // Compare key fields
            if (parsedPacket.SlaveAddress != packet.SlaveAddress)
            {
                result.Issues.Add("RTU round-trip failed: Slave Address mismatch");
            }
            
            if (parsedPacket.FunctionCode != packet.FunctionCode)
            {
                result.Issues.Add("RTU round-trip failed: Function Code mismatch");
            }
            
            if (!parsedPacket.Data.SequenceEqual(packet.Data))
            {
                result.Issues.Add("RTU round-trip failed: Data mismatch");
            }
            
            if (parsedPacket.Crc != packet.Crc)
            {
                result.Issues.Add("RTU round-trip failed: CRC mismatch");
            }
        }
        catch (Exception ex)
        {
            result.Issues.Add($"RTU round-trip test failed: {ex.Message}");
        }
    }
}

/// <summary>
/// Result of packet validation
/// </summary>
public class ValidationResult
{
    public List<string> Issues { get; } = new();
    public bool IsValid => Issues.Count == 0;
    
    public override string ToString()
    {
        if (IsValid)
        {
            return "Validation PASSED - Packet complies with Modbus specification";
        }
        
        return $"Validation FAILED - Issues found:\n{string.Join("\n", Issues.Select((issue, index) => $"{index + 1}. {issue}"))}";
    }
}
