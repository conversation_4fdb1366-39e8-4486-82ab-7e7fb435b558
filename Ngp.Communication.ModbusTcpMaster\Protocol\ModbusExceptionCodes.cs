namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Modbus exception codes as defined in the Modbus specification
/// </summary>
public static class ModbusExceptionCodes
{
    /// <summary>
    /// Illegal Function (0x01) - The function code received in the query is not an allowable action for the server
    /// </summary>
    public const byte IllegalFunction = 0x01;

    /// <summary>
    /// Illegal Data Address (0x02) - The data address received in the query is not an allowable address for the server
    /// </summary>
    public const byte IllegalDataAddress = 0x02;

    /// <summary>
    /// Illegal Data Value (0x03) - A value contained in the query data field is not an allowable value for server
    /// </summary>
    public const byte IllegalDataValue = 0x03;

    /// <summary>
    /// Slave Device Failure (0x04) - An unrecoverable error occurred while the server was attempting to perform the requested action
    /// </summary>
    public const byte SlaveDeviceFailure = 0x04;

    /// <summary>
    /// Acknowledge (0x05) - The server has accepted the request and is processing it, but a long duration of time will be required to do so
    /// </summary>
    public const byte Acknowledge = 0x05;

    /// <summary>
    /// Slave Device Busy (0x06) - The server is engaged in processing a long-duration program command
    /// </summary>
    public const byte SlaveDeviceBusy = 0x06;

    /// <summary>
    /// Memory Parity Error (0x08) - The server attempted to read record file, but detected a parity error in the memory
    /// </summary>
    public const byte MemoryParityError = 0x08;

    /// <summary>
    /// Gateway Path Unavailable (0x0A) - Gateway was unable to allocate an internal communication path from the input port to the output port for processing the request
    /// </summary>
    public const byte GatewayPathUnavailable = 0x0A;

    /// <summary>
    /// Gateway Target Device Failed to Respond (0x0B) - No response was obtained from the target device
    /// </summary>
    public const byte GatewayTargetDeviceFailedToRespond = 0x0B;

    /// <summary>
    /// Get the description of an exception code
    /// </summary>
    /// <param name="exceptionCode">Exception code</param>
    /// <returns>Human-readable description of the exception</returns>
    public static string GetDescription(byte exceptionCode)
    {
        return exceptionCode switch
        {
            IllegalFunction => "Illegal Function - The function code is not supported by the device",
            IllegalDataAddress => "Illegal Data Address - The data address is not valid for the device",
            IllegalDataValue => "Illegal Data Value - The data value is not acceptable for the device",
            SlaveDeviceFailure => "Slave Device Failure - An unrecoverable error occurred in the device",
            Acknowledge => "Acknowledge - The device is processing the request but needs more time",
            SlaveDeviceBusy => "Slave Device Busy - The device is currently busy processing another request",
            MemoryParityError => "Memory Parity Error - A parity error was detected in the device memory",
            GatewayPathUnavailable => "Gateway Path Unavailable - Gateway cannot allocate communication path",
            GatewayTargetDeviceFailedToRespond => "Gateway Target Device Failed to Respond - Target device did not respond",
            _ => $"Unknown Exception Code: 0x{exceptionCode:X2}"
        };
    }

    /// <summary>
    /// Check if an exception code is valid
    /// </summary>
    /// <param name="exceptionCode">Exception code to validate</param>
    /// <returns>True if the exception code is valid</returns>
    public static bool IsValidExceptionCode(byte exceptionCode)
    {
        return exceptionCode switch
        {
            IllegalFunction or IllegalDataAddress or IllegalDataValue or SlaveDeviceFailure or
            Acknowledge or SlaveDeviceBusy or MemoryParityError or GatewayPathUnavailable or
            GatewayTargetDeviceFailedToRespond => true,
            _ => false
        };
    }

    /// <summary>
    /// Check if an exception code indicates a temporary condition that might be retried
    /// </summary>
    /// <param name="exceptionCode">Exception code to check</param>
    /// <returns>True if the condition might be temporary and retryable</returns>
    public static bool IsRetryableException(byte exceptionCode)
    {
        return exceptionCode switch
        {
            Acknowledge or SlaveDeviceBusy or GatewayPathUnavailable => true,
            _ => false
        };
    }

    /// <summary>
    /// Check if an exception code indicates a permanent error that should not be retried
    /// </summary>
    /// <param name="exceptionCode">Exception code to check</param>
    /// <returns>True if the error is permanent and should not be retried</returns>
    public static bool IsPermanentException(byte exceptionCode)
    {
        return exceptionCode switch
        {
            IllegalFunction or IllegalDataAddress or IllegalDataValue or SlaveDeviceFailure or
            MemoryParityError or GatewayTargetDeviceFailedToRespond => true,
            _ => false
        };
    }
}

/// <summary>
/// Modbus exception that includes the exception code and description
/// </summary>
public class ModbusException : Exception
{
    /// <summary>
    /// Modbus exception code
    /// </summary>
    public byte ExceptionCode { get; }

    /// <summary>
    /// Function code that caused the exception
    /// </summary>
    public byte FunctionCode { get; }

    /// <summary>
    /// Slave address that returned the exception
    /// </summary>
    public byte SlaveAddress { get; }

    /// <summary>
    /// Initialize a new Modbus exception
    /// </summary>
    /// <param name="exceptionCode">Modbus exception code</param>
    /// <param name="functionCode">Function code that caused the exception</param>
    /// <param name="slaveAddress">Slave address that returned the exception</param>
    public ModbusException(byte exceptionCode, byte functionCode, byte slaveAddress)
        : base($"Modbus Exception - Slave {slaveAddress}, Function 0x{functionCode:X2}: {ModbusExceptionCodes.GetDescription(exceptionCode)}")
    {
        ExceptionCode = exceptionCode;
        FunctionCode = functionCode;
        SlaveAddress = slaveAddress;
    }

    /// <summary>
    /// Initialize a new Modbus exception with custom message
    /// </summary>
    /// <param name="exceptionCode">Modbus exception code</param>
    /// <param name="functionCode">Function code that caused the exception</param>
    /// <param name="slaveAddress">Slave address that returned the exception</param>
    /// <param name="message">Custom exception message</param>
    public ModbusException(byte exceptionCode, byte functionCode, byte slaveAddress, string message)
        : base(message)
    {
        ExceptionCode = exceptionCode;
        FunctionCode = functionCode;
        SlaveAddress = slaveAddress;
    }

    /// <summary>
    /// Check if this exception is retryable
    /// </summary>
    public bool IsRetryable => ModbusExceptionCodes.IsRetryableException(ExceptionCode);

    /// <summary>
    /// Check if this exception is permanent
    /// </summary>
    public bool IsPermanent => ModbusExceptionCodes.IsPermanentException(ExceptionCode);
}
